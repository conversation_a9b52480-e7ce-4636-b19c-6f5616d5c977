import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToastService, ToastPosition, CustomAction } from '../../../../../play-comp-library/src/lib/components/toast';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'app-toast-documentation',
  standalone: true,
  imports: [CommonModule, FormsModule, ButtonComponent],
  templateUrl: './app-toast.component.html',
  styleUrls: ['./app-toast.component.scss']
})
export class ToastDocumentationComponent implements OnInit {

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    // Set initial position to top-right
    this.toastService.setPosition('bottom-right');
  }

  // 1. All Toast Variants (Title + Message)
  showBasicSuccess() {
    this.toastService.success({
      title: 'Success!',
      message: 'Operation completed successfully.',
      duration: 4000
    });
  }

  showBasicError() {
    this.toastService.error({
      title: 'Error!',
      message: 'Something went wrong. Please try again.',
      duration: 4000
    });
  }

  showBasicWarning() {
    this.toastService.warning({
      title: 'Warning!',
      message: 'Please review your input carefully.',
      duration: 4000
    });
  }

  showBasicInfo() {
    this.toastService.info({
      title: 'Information',
      message: 'Here is some important information for you.',
      duration: 4000
    });
  }

  showBasicDefault() {
    this.toastService.default({
      title: 'Default Toast',
      message: 'This is a default toast with white background and black text.',
      duration: 4000
    });
  }

  showBasicCustom() {
    this.toastService.custom({
      title: 'Custom Toast',
      message: 'This is a custom toast with basic styling.',
      duration: 4000
    });
  }

  // 2. All Toast Variants (Title + Message + Action Buttons)
  showSuccessWithAction() {
    this.toastService.success({
      title: 'Success with Action',
      message: 'Operation completed. Would you like to view details?',
      duration: 6000
    }).then(result => {
      if (result.action === 'close') {
        this.toastService.info({
          title: 'Action Noted',
          message: 'Success toast was closed.',
          duration: 2000
        });
      }
    });
  }

  showErrorWithRetry() {
    this.toastService.error({
      title: 'Connection Failed',
      message: 'Unable to connect to server. Would you like to retry?',
      showRetryButton: true,
      retryButtonText: 'Retry Connection',
      duration: 6000
    }).then(result => {
      if (result.action === 'retry') {
        this.toastService.success({
          title: 'Retrying...',
          message: 'Attempting to reconnect to server.',
          duration: 3000
        });
      }
    });
  }

  showWarningWithAction() {
    this.toastService.warning({
      title: 'Unsaved Changes',
      message: 'You have unsaved changes. Do you want to save them?',
      showActionButton: true,
      actionButtonText: 'Save Changes',
      duration: 8000
    }).then(result => {
      if (result.action === 'action') {
        this.toastService.success({
          title: 'Changes Saved',
          message: 'Your changes have been saved successfully.',
          duration: 3000
        });
      }
    });
  }

  showInfoWithLearnMore() {
    this.toastService.info({
      title: 'New Feature Available',
      message: 'We have added new features to improve your experience.',
      showLearnMoreButton: true,
      learnMoreButtonText: 'Learn More',
      duration: 6000
    }).then(result => {
      if (result.action === 'learn-more') {
        this.toastService.info({
          title: 'Feature Documentation',
          message: 'Opening feature documentation...',
          duration: 3000
        });
      }
    });
  }

  showDefaultWithAction() {
    this.toastService.default({
      title: 'Default with Action',
      message: 'This is a default toast. Click to acknowledge.',
      duration: 6000
    }).then(result => {
      if (result.action === 'close') {
        this.toastService.success({
          title: 'Acknowledged',
          message: 'Default toast was acknowledged.',
          duration: 2000
        });
      }
    });
  }

  showCustomWithActions() {
    this.toastService.custom({
      title: 'Custom with Actions',
      message: 'This custom toast has multiple action buttons.',
      showCustomActions: true,
      customActions: [
        { text: 'Accept', variant: 'success', action: 'accept' },
        { text: 'Decline', variant: 'danger', action: 'decline' }
      ],
      duration: 8000
    }).then(result => {
      if (result.action === 'accept') {
        this.toastService.success({
          title: 'Accepted',
          message: 'You accepted the custom action.',
          duration: 3000
        });
      } else if (result.action === 'decline') {
        this.toastService.error({
          title: 'Declined',
          message: 'You declined the custom action.',
          duration: 3000
        });
      }
    });
  }

  // 4. Position Examples
  showPositionExample(position: ToastPosition) {
    this.toastService.setPosition(position);
    this.toastService.info({
      title: `Toast Position: ${position}`,
      message: `This toast is displayed at ${position} position.`,
      duration: 4000
    });
  }

  // 3. All Variants with All Inputs

  // Success Variants
  showSuccessWithIcon() {
    this.toastService.success({
      title: 'Success with Icon!',
      message: 'Operation completed successfully with a custom icon.',
      icon: 'check-circle',
      iconColor: 'var(--toast-success-text)',
      duration: 4000
    });
  }

  showSuccessLongDuration() {
    this.toastService.success({
      title: 'Long Duration Success',
      message: 'This success toast will stay visible for 10 seconds.',
      duration: 10000
    });
  }

  showSuccessCustomSize() {
    this.toastService.success({
      title: 'Custom Size Success',
      message: 'This success toast has custom width and height.',
      customWidth: '500px',
      customHeight: '100px',
      duration: 5000
    });
  }

  showSuccessNoProgress() {
    this.toastService.success({
      title: 'Success without Progress',
      message: 'This success toast has no progress bar.',
      showProgress: false,
      duration: 4000
    });
  }

  // Error Variants
  showErrorWithIcon() {
    this.toastService.error({
      title: 'Error with Icon!',
      message: 'Something went wrong. Please check the details.',
      icon: 'alert-circle',
      iconColor: 'var(--toast-error-text)',
      duration: 5000
    });
  }

  showErrorWithRetryAndIcon() {
    this.toastService.error({
      title: 'Connection Failed',
      message: 'Unable to connect to server. Would you like to retry?',
      icon: 'wifi-off',
      iconColor: 'var(--toast-error-text)',
      showRetryButton: true,
      retryButtonText: 'Retry Connection',
      duration: 8000
    }).then(result => {
      if (result.action === 'retry') {
        this.toastService.success({
          title: 'Retrying...',
          message: 'Attempting to reconnect to server.',
          icon: 'refresh-cw',
          duration: 3000
        });
      }
    });
  }

  showErrorCustomDuration() {
    this.toastService.error({
      title: 'Custom Duration Error',
      message: 'This error toast will stay visible for 12 seconds.',
      duration: 12000
    });
  }

  showErrorNoCloseButton() {
    this.toastService.error({
      title: 'No Close Button',
      message: 'This error toast has no close button - it will auto-dismiss.',
      showCloseButton: false,
      duration: 6000
    });
  }

  // Warning Variants
  showWarningWithIcon() {
    this.toastService.warning({
      title: 'Warning with Icon!',
      message: 'Please review your input carefully before proceeding.',
      icon: 'alert-triangle',
      iconColor: 'var(--toast-warning-text)',
      duration: 5000
    });
  }

  showWarningWithActionAndIcon() {
    this.toastService.warning({
      title: 'Unsaved Changes',
      message: 'You have unsaved changes. Do you want to save them?',
      icon: 'save',
      iconColor: 'var(--toast-warning-text)',
      showActionButton: true,
      actionButtonText: 'Save Changes',
      duration: 10000
    }).then(result => {
      if (result.action === 'action') {
        this.toastService.success({
          title: 'Changes Saved',
          message: 'Your changes have been saved successfully.',
          icon: 'check',
          duration: 3000
        });
      }
    });
  }

  showWarningCustomSize() {
    this.toastService.warning({
      title: 'Custom Size Warning',
      message: 'This warning toast has custom dimensions and will display more content.',
      customWidth: '550px',
      customHeight: '120px',
      duration: 6000
    });
  }

  showWarningLongMessage() {
    this.toastService.warning({
      title: 'Important Warning',
      message: 'This is a warning toast with a very long message that demonstrates how the toast component handles longer text content. The message will wrap appropriately and the toast will adjust its height accordingly.',
      duration: 8000
    });
  }

  // Info Variants
  showInfoWithIcon() {
    this.toastService.info({
      title: 'Information with Icon',
      message: 'Here is some important information for you.',
      icon: 'info',
      iconColor: 'var(--toast-info-text)',
      duration: 4000
    });
  }

  showInfoWithLearnMoreAndIcon() {
    this.toastService.info({
      title: 'New Feature Available',
      message: 'We have added new features to improve your experience.',
      icon: 'star',
      iconColor: 'var(--toast-info-text)',
      showLearnMoreButton: true,
      learnMoreButtonText: 'Learn More',
      duration: 8000
    }).then(result => {
      if (result.action === 'learn-more') {
        this.toastService.info({
          title: 'Feature Documentation',
          message: 'Opening feature documentation...',
          icon: 'book-open',
          duration: 3000
        });
      }
    });
  }

  showInfoQuickDuration() {
    this.toastService.info({
      title: 'Quick Info',
      message: 'This info toast will disappear quickly in 2 seconds.',
      duration: 2000
    });
  }

  showInfoCustomColors() {
    this.toastService.info({
      title: 'Custom Icon Color',
      message: 'This info toast has a custom icon color.',
      icon: 'palette',
      iconColor: '#ff6b6b',
      duration: 5000
    });
  }

  // Default Variants
  showDefaultWithIcon() {
    this.toastService.default({
      title: 'Default with Icon',
      message: 'This is a default toast with a custom icon.',
      icon: 'bell',
      iconColor: 'var(--color-text-primary)',
      duration: 4000
    });
  }

  showDefaultCustomIconColor() {
    this.toastService.default({
      title: 'Custom Icon Color',
      message: 'This default toast has a colorful icon.',
      icon: 'heart',
      iconColor: '#e74c3c',
      duration: 5000
    });
  }

  showDefaultLargeSize() {
    this.toastService.default({
      title: 'Large Default Toast',
      message: 'This default toast has larger dimensions to accommodate more content.',
      customWidth: '600px',
      customHeight: '140px',
      icon: 'maximize-2',
      duration: 6000
    });
  }

  showDefaultNoAutoDismiss() {
    this.toastService.default({
      title: 'No Auto-dismiss',
      message: 'This default toast will not auto-dismiss. You must close it manually.',
      icon: 'clock',
      duration: 0 // No auto-dismiss
    });
  }

  // Custom Variants
  showCustomGradient() {
    this.toastService.custom({
      title: '🎉 Gradient Toast',
      message: 'Beautiful gradient background with custom styling!',
      customBackground: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      customTextColor: 'white',
      icon: 'sparkles',
      iconColor: 'white',
      customWidth: '420px',
      showCustomActions: true,
      customActions: [
        { text: 'Awesome!', variant: 'secondary', action: 'awesome' }
      ],
      duration: 5000
    }).then(result => {
      if (result.action === 'awesome') {
        this.toastService.success({
          title: 'Thanks!',
          message: 'Glad you like our gradient toast!',
          duration: 3000
        });
      }
    });
  }

  showCustomDarkTheme() {
    this.toastService.custom({
      title: '🌙 Dark Mode Toast',
      message: 'Sleek dark theme with custom actions and styling.',
      customBackground: '#1a1a1a',
      customTextColor: '#ffffff',
      progressColor: '#3b82f6',
      icon: 'moon',
      iconColor: '#3b82f6',
      showCustomActions: true,
      customActions: [
        { text: 'Settings', variant: 'secondary', action: 'settings' },
        { text: 'Dismiss', variant: 'default', action: 'dismiss' }
      ],
      customWidth: '450px',
      duration: 6000
    }).then(result => {
      if (result.action === 'settings') {
        this.toastService.info({
          title: 'Settings',
          message: 'Opening settings panel...',
          duration: 2000
        });
      }
    });
  }

  showCustomBrandColors() {
    this.toastService.custom({
      title: '🚀 Brand Toast',
      message: 'Custom branded toast with company colors and style.',
      customBackground: '#6366f1',
      customTextColor: 'white',
      progressColor: '#a5b4fc',
      icon: 'rocket',
      iconColor: 'white',
      customWidth: '400px',
      duration: 5000
    });
  }

  showCustomCompact() {
    this.toastService.custom({
      title: '📱 Compact',
      message: 'Small and efficient.',
      customWidth: '280px',
      customHeight: '60px',
      icon: 'smartphone',
      duration: 3000
    });
  }



  // 5. Actions
  dismissAllToasts() {
    this.toastService.dismissAll();

    // Show confirmation after a brief delay
    setTimeout(() => {
      this.toastService.info({
        title: 'All Toasts Dismissed',
        message: 'All active toast notifications have been cleared.',
        duration: 3000
      });
    }, 500);
  }

  showMultipleToasts() {
    // Show multiple toasts with different types and timings
    this.toastService.success({
      title: 'First Toast',
      message: 'This is the first toast in the sequence.',
      duration: 6000
    });

    setTimeout(() => {
      this.toastService.info({
        title: 'Second Toast',
        message: 'This is the second toast, appearing after a delay.',
        duration: 5000
      });
    }, 1000);

    setTimeout(() => {
      this.toastService.warning({
        title: 'Third Toast',
        message: 'This is the third toast in the sequence.',
        duration: 4000
      });
    }, 2000);

    setTimeout(() => {
      this.toastService.error({
        title: 'Final Toast',
        message: 'This is the last toast in the sequence.',
        duration: 3000
      });
    }, 3000);
  }
}
